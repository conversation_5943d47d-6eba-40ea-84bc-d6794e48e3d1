import 'module-alias/register'; // This must be the first import
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import helmet from 'helmet'; // Changed import style
import cors from 'cors';    // Changed import style
import { ValidationPipe, BadRequestException, ValidationError } from '@nestjs/common'; // Import ValidationPipe
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'; // Import SwaggerModule and DocumentBuilder
import { PrismaClientExceptionFilter } from './common/filters/prisma-client-exception.filter';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { AppLoggerService } from './common/logger/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Swagger Setup
  const config = new DocumentBuilder()
    .setTitle('AI Navigator API')
    .setDescription('API documentation for the AI Navigator backend services.')
    .setVersion('1.0')
    .addBearerAuth() // If you use Bearer Authentication (e.g., JWT)
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document); // Serve Swagger UI at /api-docs

  // Add request logging middleware for debugging
  app.use((req, res, next) => {
    if (req.method === 'POST' && req.url === '/entities') {
      console.log('--- INCOMING POST /entities REQUEST ---');
      console.log('Content-Type:', req.headers['content-type']);
      console.log('Body type:', typeof req.body);
      console.log('Body:', req.body);
    }
    next();
  });

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strips properties not in DTO
      forbidNonWhitelisted: true, // Re-enabled for production security
      transform: true, // Transforms payload to DTO instance
      transformOptions: {
        enableImplicitConversion: true, // Allows conversion of path/query params to expected types
      },
      // Add detailed exception factory for debugging validation errors
      exceptionFactory: (validationErrors: ValidationError[] = []) => {
        // Log the full, detailed error to the backend console
        console.error('--- VALIDATION PIPE EXCEPTION FACTORY TRIGGERED ---');
        console.error('Number of validation errors:', validationErrors.length);
        console.error('Detailed Validation Errors:', JSON.stringify(validationErrors, null, 2));

        // Return detailed error response to client
        return new BadRequestException(validationErrors);
      },
    }),
  );

  // Register Exception Filters Globally
  const httpAdapterHost = app.get(HttpAdapterHost);
  const logger = app.get(AppLoggerService);
  app.useGlobalFilters(
    new PrismaClientExceptionFilter(httpAdapterHost, logger),
    new GlobalExceptionFilter(httpAdapterHost, logger),
  );

  // Optional: Add common middleware
  app.use(helmet());
  app.use(cors()); // Configure CORS options as needed

  // Get port from environment variable or default
  const port = process.env.PORT || 3001; // Render usually sets PORT

  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
